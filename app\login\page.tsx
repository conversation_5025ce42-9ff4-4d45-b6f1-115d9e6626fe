import GoogleSignIn from '@/app/components/GoogleSignIn';
import GitHubSignIn from '@/app/components/GitHubSignIn';
import Link from 'next/link';

export default function LoginPage() {
  return (
    <div className="flex min-h-screen flex-col items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="w-full max-w-md space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-bold tracking-tight">
            Choose Your Account
          </h2>
        </div>
        <div className="mt-8 space-y-6">
          <div className="grid grid-cols-2 gap-6">
            <GoogleSignIn />
            <GitHubSignIn />
          </div>
          <div className="text-center text-sm">
            <Link
              href="/"
              className="inline-flex items-center gap-2 font-medium text-blue-600 hover:text-blue-500 transition-colors"
              aria-label="Return to home page"
            >
              <svg
                className="w-4 h-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                aria-hidden="true"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"
                />
              </svg>
              Return to Home
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}