import GoogleSignIn from '@/app/components/GoogleSignIn';
import GitHubSignIn from '@/app/components/GitHubSignIn';
import Link from 'next/link';

export default function LoginPage() {
  return (
    <div className="min-h-screen bg-gray-50 px-4 sm:px-6 lg:px-8 relative">
      {/* Mobile/Tablet Layout (< 1024px) - Optimized for small screens */}
      <div className="lg:hidden min-h-screen flex flex-col justify-center py-8 sm:py-12">
        <div className="w-full max-w-md mx-auto">
          {/* Header section */}
          <div className="text-center mb-8 sm:mb-12 px-4 sm:px-6">
            <h2 className="text-2xl sm:text-3xl font-bold tracking-tight text-gray-900 mb-3 sm:mb-4">
              Choose Your Account
            </h2>
            <p className="text-sm sm:text-base text-gray-600">
              Sign in to access your dashboard
            </p>
          </div>

          {/* Authentication buttons - gap centered at viewport midpoint */}
          <div className="px-4 sm:px-6 mb-8 sm:mb-12">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
              <GoogleSignIn />
              <GitHubSignIn />
            </div>
          </div>

          {/* Footer section */}
          <div className="text-center px-4 sm:px-6">
            <Link
              href="/"
              className="inline-flex items-center gap-2 text-sm font-medium text-blue-600 hover:text-blue-500 transition-colors"
              aria-label="Return to home page"
            >
              <svg
                className="w-4 h-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                aria-hidden="true"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"
                />
              </svg>
              Return to Home
            </Link>
          </div>
        </div>
      </div>

      {/* Desktop Layout (≥ 1024px) - Absolute positioning for precise centering */}
      <div className="hidden lg:block">
        {/* Header section - positioned above center */}
        <div className="absolute top-0 left-0 right-0 h-1/2 flex items-end justify-center pb-12">
          <div className="text-center max-w-md w-full">
            <h2 className="text-3xl font-bold tracking-tight text-gray-900 mb-6">
              Choose Your Account
            </h2>
            <p className="text-base text-gray-600">
              Sign in to access your dashboard
            </p>
          </div>
        </div>

        {/* Authentication buttons - positioned at exact center (50% from top) */}
        <div className="absolute top-1/2 left-0 right-0 transform -translate-y-1/2 flex justify-center">
          <div className="max-w-md w-full">
            <div className="grid grid-cols-2 gap-6">
              <GoogleSignIn />
              <GitHubSignIn />
            </div>
          </div>
        </div>

        {/* Footer section - positioned below center */}
        <div className="absolute bottom-0 left-0 right-0 h-1/2 flex items-start justify-center pt-12">
          <div className="text-center">
            <Link
              href="/"
              className="inline-flex items-center gap-2 text-sm font-medium text-blue-600 hover:text-blue-500 transition-colors"
              aria-label="Return to home page"
            >
              <svg
                className="w-4 h-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                aria-hidden="true"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"
                />
              </svg>
              Return to Home
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}